{"name": "backend-rota-ms", "version": "1.0.0", "description": "This README would normally document whatever steps are necessary to get your application up and running.", "main": "index.js", "scripts": {"start:dev": "nodemon", "dev": "ts-node --files ./src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "build": "npx tsc -p . && cp -r src/email_templates build/src/email_templates && cp -r src/locales build/src/locales", "lint": "eslint --ignore-path .eslint<PERSON>ore --ext .js,.ts .", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "prepare": "husky"}, "lint-staged": {"*": "npm run lint"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"amqplib": "^0.10.5", "axios": "^1.7.9", "backend-rota-ms": "file:", "body-parser": "^1.20.3", "celebrate": "^15.0.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "express-session": "^1.18.1", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "i18n": "^0.15.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "keycloak-connect": "^26.0.7", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "mysql2": "^3.11.5", "node-cron": "^3.0.3", "puppeteer": "^23.11.1", "sequelize": "^6.37.5", "stripe": "^17.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^8.57.1", "husky": "^9.1.7", "lint-staged": "^15.4.3", "prettier": "^3.5.0", "ts-node": "^10.9.2", "typescript": "^5.7.2"}}