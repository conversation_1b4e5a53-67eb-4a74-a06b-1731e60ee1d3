import Keycloak from "keycloak-connect";
import session from "express-session";

// Session Store
const memoryStore = new session.MemoryStore();

// Keycloak configuration
const keycloakConfig: any = {
  clientId: global.config.KEYCLOAK_CLIENT_ID,
  bearerOnly: true, // backend-only authentication
  serverUrl: global.config.KEYCLOAK_SERVER_URL,
  realm: global.config.KEYCLOAK_REALM_NAME,
  credentials: {
    secret: global.config.KEYCLOAK_SECRET_KEY, // Your client secret
  },
};

// Initialize Keycloak
const keycloak = new Keycloak({ store: memoryStore }, keycloakConfig);

export { keycloak, memoryStore };
