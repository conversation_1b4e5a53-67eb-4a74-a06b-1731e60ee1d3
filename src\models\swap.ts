import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

interface SwapAttributes {
  id?: number;
  userId: number;
  status: "active" | "pending" | "deleted" | "rejected";
  adminStatus: "active" | "pending" | "deleted" | "rejected";
  toUserId?: number;
  shiftId?: number;
  roleId?: number;
  createdBy?: number;
  updatedBy?: number;
  notes: string;
  organization_id?: string;
}

export class Swap
  extends Model<SwapAttributes, never>
  implements SwapAttributes
{
  id!: number;
  userId!: number;
  status!: "active" | "pending" | "deleted" | "rejected";
  adminStatus!: "active" | "pending" | "deleted" | "rejected";
  toUserId?: number;
  shiftId?: number;
  roleId?: number;
  createdBy!: number;
  updatedBy!: number;
  notes!: string;
  organization_id?: string;
}

Swap.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
    },
    status: {
      type: DataTypes.ENUM("active", "pending", "deleted", "rejected"),
      defaultValue: "pending",
    },
    adminStatus: {
      type: DataTypes.ENUM("active", "pending", "deleted", "rejected"),
      defaultValue: "pending",
    },
    shiftId: {
      type: DataTypes.INTEGER,
    },
    roleId: {
      type: DataTypes.INTEGER,
    },
    toUserId: {
      type: DataTypes.INTEGER,
    },
    createdBy: {
      type: DataTypes.INTEGER,
    },
    updatedBy: {
      type: DataTypes.INTEGER,
    },
    notes: {
      type: DataTypes.TEXT("long"),
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize: sequelize,
    tableName: "swaps",
    modelName: "Swaps",
  },
);
