import { Request, Response } from "express";
import { DayOff, DayOffStatus } from "../models/dayOff";
import moment from "moment";
import { Op } from "sequelize";

export const addDayOff = async (req: Request, res: Response): Promise<any> => {
  try {
    const { userId, date, dayOff, notes }: any = req.body;

    const findDayOff = await DayOff.findOne({
      where: {
        userId,
        date,
        organization_id: req.user.organization_id
      },
      raw: true,
      nest: true
    });
    if (findDayOff) {
      if(findDayOff.status !== DayOffStatus.active) {
        await DayOff.update(
          {
            status: DayOffStatus.active,
            dayOff: dayOff,
          },
          {
            where: {
              id: findDayOff.id,
            },
          },
        );
        return res.status(200).send({
          status: true,
          message: res.__("SUCCESS_DATA_UPDATED"),
          data: { ...findDayOff, status: DayOffStatus.active, dayOff },
        });
      }
      if(findDayOff.dayOff !== dayOff) {
        await DayOff.update(
          {
            dayOff,
          },
          {
            where: {
              id: findDayOff.id,
            },
          },
        );
        return res.status(200).send({
          status: true,
          message: res.__("SUCCESS_DATA_UPDATED"),
          data: { ...findDayOff, status: DayOffStatus.active, dayOff },
        });
      }
      return res.status(409).send({
        status: false,
        message: res.__("DAY_OFF_EXISTS"),
      });
    }

    const newDayOff = await DayOff.create({
      userId,
      date,
      dayOff,
      notes,
      organization_id: req.user.organization_id
    });

    return res.status(201).send({
      status: true,
      message: res.__("DAY_OFF_CREATED"),
      data: newDayOff,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const getDayOffs = async (req: Request, res: Response): Promise<any> => {
  try {
    const { userId, from, to }: any = req.query;

    const where: any = {
      status: { [Op.not]: DayOffStatus.deleted },
      organization_id: req.user.organization_id
    };
    if (userId) {
      where.userId = userId;
    }
    if (from) {
      where.date = {
        [Op.gte]: moment(from).toDate(),
      };
    }
    if (to) {
      where.date = {
        [Op.lte]: moment(to).toDate(),
      };
    }

    const { count, rows: dayOffs } = await DayOff.findAndCountAll({
      where,
      order: [["date", "ASC"]],
    });

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      count,
      data: dayOffs,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

export const updateDayOff = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { id } = req.params;
    const { userId, date, dayOff, notes }: any = req.body;

    const existingDayOff = await DayOff.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      },
    });

    if (!existingDayOff) {
      return res.status(404).send({
        status: false,
        message: res.__("DAY_OFF_NOT_FOUND"),
      });
    }

    await DayOff.update(
      {
        userId,
        date,
        dayOff,
        notes,
      },
      {
        where: {
          id,
        },
      },
    );

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

// delete day-off
export const deleteDayOff = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { id } = req.params;
    const existingDayOff = await DayOff.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      },
    });
    if (!existingDayOff) {
      return res.status(404).send({
        status: false,
        message: res.__("DAY_OFF_NOT_FOUND"),
      });
    }
    await DayOff.update(
      {
        status: DayOffStatus.deleted,
      },
      {
        where: {
          id,
        },
      },
    );
    return res.send({
      status: true,
      message: res.__("DAY_OFF_DELETED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
