// import { User } from "../models/User";
import { DURATION, RABBITMQ_QUEUE, ROLE_CONSTANT } from "./constant";
import { getUserRoles } from "../keycloak/common";
import fs from "fs";
import { User } from "../models/User";
import { QueryTypes, sequelize } from "../models";
import { Op } from "sequelize";
import rabbitmq from "../rabbitmq/rabbitmq";

/** read html file */
const readHTMLFile = function (path: any, cb: any) {
  /** Read the HTML file at the specified path using UTF-8 encoding */
  fs.readFile(path, "utf-8", function (err, data) {
    /** If an error occurs during reading, log the error and throw it */
    if (err) {
      console.log(err);
      throw err; // Stop the execution and throw the error
    } else {
      /** If no error, pass the file content to the callback function */
      cb(null, data); // call the callback function with the file data
    }
  });
};

/** Get UserId from User table */
const getUserIdFromUserTable = async (userId: any) => {
  try {
    const getUser: any = await User.findOne({
      where: {
        keycloak_userId: userId,
      },
      raw: true,
    });
    if (!getUser) {
      return { status: false, message: "ERROR_USER_NOT_FOUND_IN_KEYCLOAK" };
    }
    return { status: true, data: getUser };
  } catch (e) {
    console.log("Exception: ", e);
    return null;
  }
};

const getPagination = (page: number, size: number) => {
  const limit = size;
  const Page = page || 1;
  const offset = (Page - 1) * limit;
  return { limit, offset };
};

const getPaginatedItems = (
  pageSize: number,
  pageNumber: number,
  total: number,
) => {
  return {
    pageNumber: pageNumber,
    per_page: pageSize,
    total: total,
    total_pages: Math.ceil(total / pageSize),
  };
};

const getDurationBasedOnDays = (days: number) => {
  if (days <= 7) {
    return DURATION.WEEKLY; // Store as weekly for 7 days or less
  } else if (days < 365) {
    return DURATION.MONTHLY; // Store as monthly for days greater than 7 but less than 365
  } else {
    return DURATION.YEARLY; // Store as yearly for 365 days or more
  }
};

/** Check user role status and return response accordingly. */
const checkUserRole = async (userId: any, token: any) => {
  try {
    const getRoles: any = await getUserRoles(userId, token);
    const masterRole: any = getRoles.data.realmMappings.find(
      (role: any) =>
        role.name === global.config.KEYCLOAK_SUPER_ADMIN_ROLE &&
        role.description ===
          global.config.KEYCLOAK_SUPER_ADMIN_ROLE_DESCRIPTION,
    );

    if (!masterRole) {
      return false;
    }
    return true;
  } catch (e) {
    console.log("user role status Exception: ", e);
    return { status: false, message: e };
  }
};

const getUser = async (id: any, isAuth: boolean = false) => {
  const findUser = await sequelize.query(
    `SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE ${isAuth ? `keycloak_auth_id='${id}'` : `id = ${id}`} AND user_status NOT IN ('cancelled', 'deleted')`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );

  return findUser && findUser.length > 0 ? findUser[0] : null;
};

const getUsers = async (ids: any) => {
  const findUsers = await sequelize.query(
    `SELECT id, user_first_name, user_middle_name, user_last_name, user_email, branch_id, department_id, IF((user_avatar IS NOT NULL AND user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = user_avatar)), '') AS user_avatar_link, user_avatar, user_status, user_active_role_id, web_user_active_role_id, webAppToken, appToken, concat(user_first_name, " ", user_last_name) as user_full_name, rota_group_by, list_order, organization_id FROM nv_users WHERE id IN (${ids.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findUsers;
};

const getRoles = async (id: any) => {
  const findRoles = await sequelize.query(
    `SELECT * FROM nv_roles WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findRoles;
};

const getBranchDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_branches WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findBranches;
};

const getDepartmentDetails = async (id: any) => {
  const findBranches = await sequelize.query(
    `SELECT * FROM nv_departments WHERE id IN (${id?.join(",")})`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return findBranches;
};

const getUserAllRoles = async (userId: number) => {
  const userRoles = await sequelize.query(
    `SELECT r.id, r.role_name FROM nv_user_roles ur INNER JOIN nv_roles r ON ur.role_id = r.id WHERE ur.user_id = ${userId}`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return userRoles;
};

const getUserSession = async (token: string, deviceType: string) => {
  // const userSession = await UserSession.findOne({ where: { token, device_type: deviceType } });
  const userSession = await sequelize.query(
    `SELECT * FROM nv_user_session WHERE token = '${token}' AND device_type = '${deviceType}'`,
    {
      type: QueryTypes.SELECT,
      raw: true,
    },
  );
  return userSession && userSession.length > 0 ? userSession[0] : null;
};

const createNotification = async (data: any, req:any, deviceId: string) => {
  // await sequelize.query(
  //   `INSERT INTO notifications (${Object.keys(data).join(",")}) VALUES (${Object.values(
  //     data,
  //   )
  //     .map((value) => `'${value}'`)
  //     .join(",")}) `,
  //   {
  //     type: QueryTypes.INSERT,
  //     returning: true,
  //   },
  // );
  try {
    const notificationObj: any = {
      title: data?.notification_subject,
      description: data.notification_content,
      redirection_object: data?.redirection_object ? JSON.stringify(data?.redirection_object) : null,
      notification_image: null,
      from_user_id: data?.from_user_id,
      to_user_id: data?.to_user_id,
      notification_status: 'sent',
      notification_type: data?.notification_type,
      redirection_type: data?.redirection_type || null,
      reference_id: data?.reference_id || null,
      created_by: req && req.user ? req.user.id : req,
      updated_by: req && req.user ? req.user.id : req,
      notification_meta_id: null,
      deviceId: deviceId
    }
    await rabbitmq.publishMessage(
          RABBITMQ_QUEUE.SHIFT_NOTIFICATION,
          notificationObj,
    );
 
  } catch (error) {
    console.log("error", error);
    return false;
  }
};

const getUserWeekDays = async (userId: number, day: any) => {
  return await sequelize.query(`(SELECT * FROM nv_user_week_day WHERE user_id = ${userId} AND ${day} !='working' AND user_weekday_status='active')`, {
    type: QueryTypes.SELECT,
    raw: true,
  })
}

const permittedForAdmin = async (user_id: number, roleArray?: any) => {
  try {
    const query = `
  SELECT ur.*, r.role_name FROM nv_user_roles ur
  INNER JOIN nv_roles r ON ur.role_id = r.id
  WHERE ur.user_id = :user_id
  AND r.role_name IN (:roleArray)
`;

    const roleData = await sequelize.query(query, {
      replacements: { user_id, roleArray },
      type: QueryTypes.SELECT,
    });

    if (roleData.length > 0) {
      const roleNames = roleData.map((role: any) => role.role_name);

      if (
        roleNames.includes(ROLE_CONSTANT.DIRECTOR) ||
        roleNames.includes(ROLE_CONSTANT.HR)
      ) {
        const userQuery = `
      SELECT id FROM nv_users 
      WHERE id = :user_id 
      AND user_status NOT IN (:statuses)
    `;

        const userStatus = await sequelize.query(userQuery, {
          replacements: {
            user_id,
            statuses: ["pending", "deleted", "cancelled"],
          },
          type: QueryTypes.SELECT,
        });

        if (userStatus.length > 0) {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return false;
    }
  } catch (error) {
    console.log("error", error);
  }
};

export {
  getUserIdFromUserTable,
  getPaginatedItems,
  getPagination,
  getDurationBasedOnDays,
  readHTMLFile,
  checkUserRole,
  getRoles,
  getUser,
  getUserAllRoles,
  getUserSession,
  createNotification,
  getBranchDetails,
  getUsers,
  permittedForAdmin,
  getDepartmentDetails,
  getUserWeekDays
};
