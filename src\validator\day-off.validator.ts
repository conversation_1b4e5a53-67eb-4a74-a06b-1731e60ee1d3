import { celebrate, Joi, Segments } from "celebrate";

export const addDayOffValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      userId: Joi.number().required(),
      date: Joi.date().required(),
      dayOff: Joi.boolean().required(),
      notes: Joi.string().allow(null, "").optional(),
    }),
  });

export const updateDayOffValidator = () =>
  celebrate({
    [Segments.BODY]: Joi.object().keys({
      userId: Joi.number().optional(),
      date: Joi.date().optional(),
      dayOff: Joi.boolean().optional(),
      notes: Joi.string().allow(null, "").optional(),
    }),
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });

export const deleteDayOffValidator = () =>
  celebrate({
    [Segments.PARAMS]: Joi.object().keys({
      id: Joi.number().required(),
    }),
  });
