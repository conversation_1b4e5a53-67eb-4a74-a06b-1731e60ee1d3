import { celebrate, Joi, Segments } from "celebrate";

export default {
  addShiftValidator: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        userId: Joi.when("isOpen", {
          is: Joi.exist().valid(true),
          then: Joi.string().allow(null, ""),
          otherwise: Joi.number().required(),
        }),
        startTime: Joi.date().required(),
        endTime: Joi.date().required(),
        status: Joi.string()
          .valid("active", "pending", "deleted")
          .default("active"),
        minutesBreak: Joi.number().optional().allow(null, 0),
        branchId: Joi.number().optional().allow(null, ""),
        departmentId: Joi.number().optional().allow(null, ""),
        role: Joi.number().optional().allow(null, ""),
        isOpen: Joi.boolean().optional(),
        isPublished: Joi.boolean().optional(),
        isSwap: Joi.boolean().optional(),
        acknowledged: Joi.boolean().optional(),
        notes: Joi.string().allow(null, "").optional(),
      }),
    }),
  updateShiftValidator: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        userId: Joi.when("isOpen", {
          is: Joi.exist().valid(true),
          then: Joi.string().allow(null, ""),
          otherwise: Joi.number().required(),
        }),
        startTime: Joi.date().optional().allow(null, ""),
        endTime: Joi.date().optional().allow(null, ""),
        status: Joi.string()
          .valid("active", "pending", "deleted")
          .default("active"),
        minutesBreak: Joi.number().optional().allow(null, 0),
        branchId: Joi.number().optional().allow(null, ""),
        departmentId: Joi.number().optional().allow(null, ""),
        role: Joi.number().optional().allow(null, ""),
        isOpen: Joi.boolean().optional().allow(null, ""),
        isPublished: Joi.boolean().optional().allow(null, ""),
        isSwap: Joi.boolean().optional().allow(null, ""),
        isClaim: Joi.boolean().optional().allow(null, ""),
        acknowledged: Joi.boolean().optional().allow(null, ""),
        notes: Joi.string().allow(null, "").optional(),
      }),
      [Segments.PARAMS]: Joi.object().keys({
        id: Joi.number().required(),
      }),
    }),
  publishUnpublishShift: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        isPublished: Joi.boolean().required(),
      }),
      [Segments.PARAMS]: Joi.object().keys({
        id: Joi.number().required(),
      }),
    }),
  checkAvailability: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        id: Joi.number().allow(null, "").optional(),
        userId: Joi.when("isOpen", {
          is: Joi.exist().valid(true),
          then: Joi.string().allow(null, ""),
          otherwise: Joi.number().required(),
        }),
        startTime: Joi.date().optional(),
        endTime: Joi.date().optional(),
        status: Joi.string()
          .valid("active", "pending", "deleted")
          .default("active"),
        isOpen: Joi.boolean().optional(),
      }),
    }),
  swapShift: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        userId: Joi.number().required(),
        toUserId: Joi.number().required(),
        roleId: Joi.number().required(),
        shiftId: Joi.number().required(),
      }),
    }),
  swapAction: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        id: Joi.number().required(),
        status: Joi.string().valid("active", "deleted", "rejected"),
      }),
    }),
  copyShiftsRangeValidator: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        fromStartDate: Joi.date().required(),
        fromEndDate: Joi.date().required(),
        toStartDate: Joi.date().required(),
        toEndDate: Joi.date().required(),
        selectedEmployees: Joi.array().items(Joi.number()).optional(),
        copyDaysOff: Joi.boolean().default(false),
        shiftClashStrategy: Joi.string().valid('clear', 'skip', 'overwrite', 'openshift').required()
      }),
    }),
  updateUserOrderList: () =>
    celebrate({
      [Segments.BODY]: Joi.object().keys({
        userId: Joi.number().required(),
        sortOrder: Joi.number().min(0).required()
      })
    })
};

