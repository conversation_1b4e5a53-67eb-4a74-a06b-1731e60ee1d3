import swaggerJsdoc from "swagger-jsdoc";

export const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "Express API",
      version: "1.0.0",
      description: "API documentation for the Express app",
    },
    servers: [
      {
        url: "http://localhost:9027",
        description: "local development server",
      },
      {
        url: "https://staging.namastevillage.theeasyaccess.com",
        description: "staging server",
      },
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },
  },
  apis: ["./src/routes/**/*.ts"],
  security: [
    {
      BearerAuth: [],
    },
  ],
};

export const swaggerSpec = swaggerJsdoc(swaggerOptions);

// // Write the Swagger spec to a file
// const outputPath = path.join(__dirname, "swagger-output.json");
// console.log("outputPath", outputPath);
// fs.writeFileSync(outputPath, JSON.stringify(swaggerSpec, null, 2));

// console.log(`Swagger spec has been saved to ${outputPath}`);

export default swaggerSpec;
