import express from "express";
import {
  createAvailability,
  deleteAvailability,
  getAvailabilities,
  getAvailabilityById,
  updateAvailability,
} from "../../controller/availability.controller";

const router = express.Router();

// Create new availability
import {
  addAvailabilityValidator,
  deleteAvailabilityValidator,
  updateAvailabilityValidator,
} from "../../validator/availability.validator";

router.post("/", addAvailabilityValidator(), createAvailability);

// Get all availabilities
router.get("/", getAvailabilities);

// Get specific availability by ID
router.get("/:id", getAvailabilityById);

// Update availability
router.put("/:id", updateAvailabilityValidator(), updateAvailability);

// Delete availability
router.delete("/:id", deleteAvailabilityValidator(), deleteAvailability);

export default router;
