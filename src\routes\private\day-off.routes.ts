import express from "express";
import {
  getDayOffs,
  addDayOff,
  updateDayOff,
  deleteDayOff,
} from "../../controller/day-off.controller";
import {
  addDayOffValidator,
  deleteDayOffValidator,
  updateDayOffValidator,
} from "../../validator/day-off.validator";

const router = express.Router();

router.get("/", getDayOffs);
router.post("/", addDayOffValidator(), addDayOff);
router.put("/:id", updateDayOffValidator(), updateDayOff);
router.delete("/:id", deleteDayOffValidator(), deleteDayOff);

export default router;
