import { Router } from "express";
import {
  getShifts,
  addShift,
  updateShift,
  checkShiftAvailability,
  setAsOpenShift,
  publishUnpublishShift,
  swapShift,
  swapActions,
  dropShift,
  getShiftHistory,
  copyShiftsRange,
  checkCopyShiftTimeConflict,
  deleteShift,
  clearWeekShifts,
  dropAction,
  claimOpenShift,
  exportShiftsToExcel,
  changeGroupBy,
  updateUserOrderList
} from "../../controller/shift.controller";
import shiftValidator from "../../validator/shift.validator";

const router = Router();

router.get("/", getShifts);
router.post("/", shiftValidator.addShiftValidator(), addShift);
router.put("/set-as-open-shift/:id", setAsOpenShift);
router.put("/drop-shift/:id", dropShift);
router.put("/drop-action/:id", dropAction);
router.put("/publish-unpublish", publishUnpublishShift);
router.put("/:id", shiftValidator.updateShiftValidator(), updateShift);

router.delete("/:id", deleteShift);
router.post(
  "/check-availability",
  shiftValidator.checkAvailability(),
  checkShiftAvailability,
);
router.post("/swap-shift", shiftValidator.swapShift(), swapShift);
router.post("/swap-action", shiftValidator.swapAction(), swapActions);

router.post("/check-bulk-availability", checkCopyShiftTimeConflict);

router.post("/copy-shifts-range", shiftValidator.copyShiftsRangeValidator(), copyShiftsRange);

router.delete("/clear-week-shifts", clearWeekShifts);

router.get("/history/:id", getShiftHistory);

router.get("/claim/:id", claimOpenShift);

router.get("/export-shifts-to-excel", exportShiftsToExcel);

router.post("/change-group-by", changeGroupBy);

router.post(
  "/update-user-order",
  shiftValidator.updateUserOrderList(),
  updateUserOrderList
);

export default router;

