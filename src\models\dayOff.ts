import { Model, DataTypes } from "sequelize";
import { sequelize } from "./index";

export enum DayOffStatus {
  active = "active",
  pending = "pending",
  deleted = "deleted",
}
interface DayOffAttributes {
  id?: number;
  userId: number;
  status: "active" | "pending" | "deleted";
  date: Date;
  dayOff: boolean;
  createdBy?: number;
  updatedBy?: number;
  notes: string;
  organization_id?: string;
}

export class DayOff
  extends Model<DayOffAttributes, never>
  implements DayOffAttributes
{
  id!: number;
  userId!: number;
  status!: "active" | "pending" | "deleted";
  date!: Date;
  dayOff!: boolean;
  createdBy!: number;
  updatedBy!: number;
  notes!: string;
  organization_id?: string;
}

DayOff.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
    },
    status: {
      type: DataTypes.ENUM(Object.values(DayOffStatus)),
      defaultValue: DayOffStatus.active,
    },
    date: {
      type: DataTypes.DATEONLY,
    },
    dayOff: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
    },
    updatedBy: {
      type: DataTypes.INTEGER,
    },
    notes: {
      type: DataTypes.TEXT("long"),
    },
    organization_id: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  },
  {
    sequelize: sequelize,
    tableName: "day_off",
    modelName: "DayOff",
  },
);
