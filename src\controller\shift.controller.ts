import { Op, QueryTypes } from "sequelize";
import { Request, Response } from "express";
import { Shift, ShiftStatus } from "../models/shifts";
import moment from "moment";
import {
  createNotification,
  getBranchDetails,
  getDepartmentDetails,
  getRoles,
  getUser,
  getUsers,
  getUserWeekDays,
  permittedForAdmin,
} from "../helper/common";
import { Swap } from "../models/swap";
import { DayOff, DayOffStatus } from "../models/dayOff";
import i18n from "../helper/i18n";
import {
  NOTIFICATIONS,
  RABBITMQ_QUEUE,
  ROLE_CONSTANT,
} from "../helper/constant";
import rabbitmq from "../rabbitmq/rabbitmq";
import _ from "lodash";
import ShiftHistory from "../models/shiftHistory";
import { Drop } from "../models/drop";
import { Availability, AvailabilityStatus } from "../models/availability";
import ExcelJS from 'exceljs';
import { sequelize } from "../models";

const commonShiftValidator = async (req: any) => {
  try {
    const { userId, startTime, endTime, isOpen, swapId, id } = req.body;

    const findShift: any = swapId
      ? await Shift.findOne({ 
          where: { 
            id: swapId,
            organization_id: req.user.organization_id 
          } 
        })
      : {};

      const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN
    ]);

    if (!checkPermission) {
      if (startTime >= endTime) {
        return {
          data: null,
          message: i18n.__("START_TIME_MUST_BE_BEFORE_END_TIME"),
        };
      }

      if (startTime < new Date()) {
        return {
          data: null,
          message: i18n.__("START_TIME_MUST_BE_AFTER_TODAY"),
        };
      }

      if (endTime < new Date()) {
        return {
          data: null,
          message: i18n.__("END_TIME_MUST_BE_AFTER_TODAY"),
        };
      }
    }

    const existingWhere: any = {
      [Op.or]: [
        {
          startTime: {
            [Op.and]: {
              [Op.gte]:
                findShift && findShift.startTime
                  ? new Date(findShift.startTime)
                  : new Date(startTime),
              [Op.lt]:
                findShift && findShift.endTime
                  ? new Date(findShift.endTime)
                  : new Date(endTime),
            },
          },
        },
        {
          endTime: {
            [Op.and]: {
              [Op.gt]:
                findShift && findShift.startTime
                  ? new Date(findShift.startTime)
                  : new Date(startTime),
              [Op.lte]:
                findShift && findShift.endTime
                  ? new Date(findShift.endTime)
                  : new Date(endTime),
            },
          },
        },
        {
          startTime: {
            [Op.lte]:
              findShift && findShift.startTime
                ? new Date(findShift.startTime)
                : new Date(startTime),
          },
          endTime: {
            [Op.gt]:
              findShift && findShift.endTime
                ? new Date(findShift.endTime)
                : new Date(endTime),
          },
        },
      ],
      // status: status ? status : "active",
      isOpen: isOpen == true ? true : false,
      isDropped: false,
      organization_id: req.user.organization_id
    };

    if (id) {
      existingWhere.id = {
        [Op.ne]: id,
      };
    }

    if (!existingWhere.isOpen && userId) {
      existingWhere.userId = userId;
    }
    const existingShift = await Shift.findOne({
      where: existingWhere,
    });

    if (userId) {
      // const checkDayOff = await DayOff.findOne({
      //   where: {
      //     userId,
      //     status: DayOffStatus.active,
      //     date: moment(startTime).format("YYYY-MM-DD"),
      //   },
      // });
      // const checkUserWeekDay = await getUserWeekDays(userId, `${moment(startTime).format('dddd')}`.toLowerCase())

      // if ((checkDayOff && checkDayOff.dayOff) || checkUserWeekDay.length > 0) {
      //   return {
      //     data: null,
      //     message: `User is on day off`,
      //   };
      // }
      const findUser = await getUser(userId);
      if (!findUser) {
        return {
          data: null,
          message: i18n.__("ERROR_USER_NOT_FOUND"),
        };
      }
    }

    if (existingShift) {
      return {
        data: existingShift,
        message: `Shift time clash detected ${moment(startTime).format("HH:mm A")} to ${moment(endTime).format("HH:mm A")}`,
      };
    }

    return null;
  } catch (error: any) {
    console.log(error);
    return {
      data: null,
      message: error?.message,
    };
  }
};

const addShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const body = req.body;

    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
    ]);
    if (!checkPermission)
      return res
        .status(403)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const {
      userId,
      startTime,
      endTime,
      status,
      minutesBreak,
      branchId,
      departmentId,
      role,
      isOpen,
      isPublished,
      isSwap,
      acknowledged,
      notes,
    } = body;

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
      });
    }
    const findUser = await getUser(userId);

    const newShift = await Shift.create({
      userId,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      status,
      minutesBreak,
      branchId,
      departmentId,
      roleId: role,
      isOpen: isOpen ? true : false,
      isPublished: isPublished ? true : false,
      isSwap: isSwap ? true : false,
      acknowledged: acknowledged ? true : false,
      isDropped: false,
      createdBy: req?.user?.id,
      updatedBy: req?.user?.id,
      notes: notes,
      organization_id: req.user.organization_id,
    });

    const changes = ShiftHistory.generateChanges({}, newShift);
    await ShiftHistory.logChange(newShift.id, req.user?.id, "CREATE", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const notificationObj = {
      deviceId: findUser?.webAppToken
        ? findUser?.webAppToken
        : findUser?.appToken,
      content: NOTIFICATIONS.SHIFT_ADD.CONTENT(
        findUser?.user_first_name,
        moment(startTime).format("YYYY-MM-DD HH:mm A"),
        moment(endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_ADD.CONTENT(
          findUser?.user_first_name,
          moment(startTime).format("YYYY-MM-DD HH:mm A"),
          moment(endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
        notification_image: null,
        to_user_id: userId,
        redirection_type: "shift_details",
        notification_type: "shifts_add",
        notification_status: "sent",
        reference_id: newShift.id,
        redirection_object: { id: newShift.id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_ADD.HEADER,
    };

    if (isPublished && acknowledged && !isOpen) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(201).send({
      status: true,
      message: res.__("SHIFT_ADDED"),
      // data: newShift,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const getShifts = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      userId,
      from,
      to,
      role,
      branchId,
      departmentId,
      isAdmin,
      isOpen,
      isDashboard,
      sortBy,
      sortOrder,
      status,
      shiftId,
      includeAvailability
    }: any = req.query;

    let where: any = {
      status: status ? status : { [Op.not]: ShiftStatus.deleted },
      organization_id: req.user.organization_id
    };

    if(isDashboard && isDashboard === "true") {
      where = {
        ...where,
        isPublished: true
      }
    }

    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER
    ]);

    if (userId && role) {
      where = {
        ...where,
        [Op.or]: [
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
          },
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: { [Op.not]: role },
          },
          {
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
            isOpen: true,
          }
        ]
      }
    } else if (!userId && role) {
      where = {
        ...where,
        roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role
      }
    } else if (userId && !role) {
      if (isAdmin === "true" && checkPermission) {
        where = {
          ...where,
          userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId
        }
      } else {
        where = {
          ...where,
          userId: req?.user?.id
        }
      }
    }

    // Preserve admin and user filtering logic
    if (isAdmin === "true" && checkPermission) {
      if (userId && !role) where.userId = userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId;
    }
    //  else {
    //   // if (!role && isOpen !== "true" && !userId) where.userId = req?.user?.id;

    // }
    if (!checkPermission || isAdmin != "true") {
      where.isPublished = 1;
    }

    // Preserve `isOpen` condition
    if (isOpen === "true") {
      where.isOpen = true;
      where.userId = { [Op.is]: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId ? userId : null };
    }

    // Preserve role and location filtering
    // if (role) where.roleId = role;
    if (branchId) where.branchId = branchId?.toString()?.split(",")?.length > 1 ? { [Op.in]: branchId?.toString()?.split(",")?.map(Number) } : branchId;
    if (departmentId) where.departmentId = departmentId;

    // Preserve date range filtering
    if (from)
      where.startTime = { [Op.gte]: moment(from).startOf("day").toDate() };
    if (to) where.endTime = { [Op.lte]: moment(to).endOf("day").toDate() };

    if (shiftId) where = { id: shiftId, organization_id: req.user.organization_id }

    // Fetch shifts (Main Query)
    const { rows: shifts, count }: any = await Shift.findAndCountAll({
      where,
      order: [
        [
          sortBy ? sortBy : "startTime",
          sortOrder && sortOrder != ""
            ? String(sortOrder).toUpperCase()
            : "ASC",
        ],
      ],
      raw: true,
      nest: true,
    });

    // Extract unique IDs for batch fetching
    const userIds = [
      ...new Set(shifts.map((s: any) => s.userId).filter(Boolean)),
    ];
    const roleIds = [
      ...new Set(shifts.map((s: any) => s.roleId).filter(Boolean)),
    ];
    const locationIds = [
      ...new Set(shifts.map((s: any) => s.branchId).filter(Boolean)),
    ];
    const departmentIds = [
      ...new Set(shifts.map((s: any) => s.departmentId).filter(Boolean)),
    ];
    const shiftIds = shifts.map((s: any) => s.id);

    // Batch fetch related data
    const [users, roles, locations, departments, swaps, drops] = await Promise.all([
      userIds.length ? getUsers(userIds) : [],
      roleIds.length ? getRoles(roleIds) : [],
      locationIds.length ? getBranchDetails(locationIds) : [],
      departmentIds.length ? getDepartmentDetails(departmentIds) : [],
      shiftIds.length
        ? Swap.findAll({
          where: {
            shiftId: shiftIds
          },
          raw: true,
          nest: true,
        })
        : [],
      shiftIds.length ? Drop.findAll({
        where: {
          shiftId: shiftIds
        },
        raw: true,
        nest: true,
      }) : [],
    ]);

    // Create lookup maps for faster access
    const userMap = Object.fromEntries(users.map((u: any) => [u.id, u]));
    const roleMap = Object.fromEntries(roles.map((r: any) => [r.id, r]));
    const locationMap = Object.fromEntries(
      locations.map((l: any) => [l.id, l]),
    );
    const departmentsMap = Object.fromEntries(
      departments.map((l: any) => [l.id, l]),
    );

    // Group swaps by shift ID
    const swapMap = swaps.reduce((acc: any, swap: any) => {
      if (!acc[swap.shiftId]) acc[swap.shiftId] = [];
      acc[swap.shiftId].push(swap);
      return acc;
    }, {});

    const dropMap = drops.reduce((acc: any, drop: any) => {
      if (!acc[drop.shiftId]) acc[drop.shiftId] = [];
      acc[drop.shiftId].push(drop);
      return acc;
    }, {});

    // Prepare to fetch availability data if requested
    let availabilityData: Record<string, any[]> = {};
    if (includeAvailability === "true") {
      // Apply the same filters to availability as applied to shifts
      const availabilityWhere: any = {
        status: AvailabilityStatus.active
      };

      // Apply user filter to availability
      if (userId) {
        availabilityWhere.userId = userId;
      } else if (!checkPermission || isAdmin !== "true") {
        availabilityWhere.userId = req?.user?.id;
      }

      // Apply date range filters to availability
      if (from) {
        availabilityWhere.date = {
          ...availabilityWhere.date,
          [Op.gte]: moment(from).startOf("day").toDate()
        };
      }
      if (to) {
        availabilityWhere.date = {
          ...availabilityWhere.date,
          [Op.lte]: moment(to).endOf("day").toDate()
        };
      }

      // Fetch availability data
      try {
        const availabilities = await Availability.findAll({
          where: availabilityWhere,
          order: [["date", "ASC"]],
          raw: true,
        });

        // Group availabilities by date for easy lookup
        availabilityData = availabilities.reduce((acc: Record<string, any[]>, availability: any) => {
          const dateKey = moment(availability.date).format("YYYY-MM-DD");
          availability.user = userMap[availability.userId] || null;
          if (!acc[dateKey]) acc[dateKey] = [];
          acc[dateKey].push(availability);
          return acc;
        }, {});
      } catch (err) {
        // Log error but continue processing
        console.log(err);
      }
    }

    // Process shifts while preserving logic
    const processedShifts = await Promise.all(
      shifts.map(async (shift: any) => {
        shift.date = moment(shift.startTime).format("YYYY-MM-DD"); // Format date without moment
        shift.user = userMap[shift.userId] || null;
        shift.role = roleMap[shift.roleId] || null;
        shift.branch = locationMap[shift.branchId] || null;
        shift.department = departmentsMap[shift.departmentId] || null;

        if (includeAvailability === "true" && availabilityData[shift.date] && isDashboard !== "true") {
          shift.availability = availabilityData[shift.date];
        }
        // if (isDashboard !== "true") {
        // Process swap requests
        if (swapMap[shift.id]) {
          shift.swapRequests = await Promise.all(
            swapMap[shift.id].map(async (swap: any) => ({
              ...swap,
              from: userMap[swap.userId]
                ? userMap[swap.userId]
                : await getUser(swap.userId),
              to: userMap[swap.toUserId]
                ? userMap[swap.toUserId]
                : await getUser(swap.toUserId),
              role: roleMap[swap.roleId]
                ? roleMap[swap.roleId]
                : await getRoles(swap.roleId)?.then((roles: any) => roles && roles.length > 0 ? roles[0] : null),
            })),
          );
        } else {
          shift.swapRequests = [];
        }
        if (dropMap[shift.id]) {
          shift.drops = await Promise.all(
            dropMap[shift.id].map(async (drop: any) => ({
              ...drop,
              createdBy: userMap[drop.createdBy]
                ? userMap[drop.createdBy]
                : await getUser(drop.createdBy),
              updatedBy: userMap[drop.updatedBy]
                ? userMap[drop.updatedBy]
                : await getUser(drop.updatedBy),
            })),
          );
        }
        // }
        return shift;
      }),
    );

    // Handle dashboard view
    let dashboardData;
    if (isDashboard === "true") {
      // First group shifts by date
      const dateGroups = processedShifts.reduce((acc: any, shift: any) => {
        if (!acc[shift.date]) {
          acc[shift.date] = { date: shift.date, count: 0, shifts: [] };
        }
        acc[shift.date].count++;
        if (acc[shift.date].shifts.length < 5) {
          acc[shift.date].shifts.push(shift);
        }
        return acc;
      }, {});

      // Add availability data to each date group
      if (includeAvailability === "true") {
        Object.keys(availabilityData).forEach(date => {
          if (!dateGroups[date]) {
            // If there are no shifts for this date but there is availability data
            dateGroups[date] = {
              date,
              count: 0,
              shifts: [],
              availability: availabilityData[date]
            };
          } else {
            // If there are shifts for this date, add the availability data
            dateGroups[date].availability = availabilityData[date];
          }
        });
      }

      dashboardData = Object.values(dateGroups);
    }

    // Preserve response logic
    const data = isDashboard === "true" ? dashboardData : processedShifts;

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      ...(req?.user?.rota_group_by ? { groupBy: req?.user?.rota_group_by } : {}) ,
      count,
      data: data,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const deleteShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
      ROLE_CONSTANT.BRANCH_MANAGER,
    ]);

    if (req.user.id != findShift.userId) {
      if (!checkPermission) {
        return res.status(403).send({
          status: false,
          message: res.__("PERMISSION_DENIED"),
        });
      }
    }


    await Shift.update(
      {
        status: ShiftStatus.deleted,
      },
      {
        where: {
          id,
          organization_id: req.user.organization_id
        },
      }
    );

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DELETED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send(
      {
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        error,
      },
    )
  }
};

const clearWeekShifts = async (req: Request, res: Response): Promise<any> => {
  try {
    const { startTime, endTime } = req.body;
    
    // Ensure we have valid date strings
    if (!startTime || !endTime) {
      return res.status(400).send({
        status: false,
        message: res.__("START_TIME_AND_END_TIME_REQUIRED"),
      });
    }

    const where: any = {
      organization_id: req.user.organization_id
    }
    if (startTime)
      where.startTime = { [Op.gte]: moment(startTime).startOf("day").toDate() };
    if (endTime) where.endTime = { [Op.lte]: moment(endTime).endOf("day").toDate() };

    const findShift = await Shift.findAll({
     where: where
    });
    
    console.log(`Found ${findShift.length} shifts`);
    
    if (!findShift.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    await Shift.update(
      {
        status: ShiftStatus.deleted,
      },
      {
        where: {
          id: { [Op.in]: findShift.map((shift: any) => shift.id) },
          organization_id: req.user.organization_id
        },
      }
    );

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DELETED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send(
      {
        status: false,
        message: res.__("SOMETHING_WENT_WRONG"),
        error,
      },
    )
  }
};

const updateShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    let {
      userId,
      startTime,
      endTime,
      status,
      branchId,
      departmentId,
      role,
      isOpen,
      isPublished,
      acknowledged,
      notes,
    } = req.body;
    const { minutesBreak } = req.body

    req.body.id = id;
    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      if (validateShift?.data?.id !== id) {
        return res.status(409).send({
          status: false,
          message: validateShift?.message,
        });
      }
    }

    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    userId = userId ? userId : findShift.userId;
    startTime = startTime ? startTime : findShift.startTime;
    endTime = endTime ? endTime : findShift.endTime;
    status = status ? status : findShift.status;
    branchId = branchId ? branchId : findShift.branchId;
    departmentId = departmentId ? departmentId : findShift.departmentId;
    role = role ? role : findShift.role;
    notes = notes ? notes : findShift.notes;
    isOpen = isOpen ? true : false;
    isPublished = isPublished ? true : false;
    acknowledged = acknowledged ? true : false;

    const findUser = await getUser(userId);
    const updateObj = {
      userId,
      startTime: new Date(startTime),
      endTime: new Date(endTime),
      status,
      minutesBreak: minutesBreak,
      branchId,
      departmentId,
      roleId: role,
      isOpen: isOpen,
      isPublished: isPublished,
      isSwap: false,
      isDrop: false,
      acknowledged: acknowledged,
      updatedBy: req?.user?.id,
      notes: notes,
    };

    await Shift.update(updateObj, {
      where: {
        id,
        organization_id: req.user.organization_id
      },
    });

    await Swap.update({ status: 'deleted' }, { 
      where: { 
        shiftId: id,
        organization_id: req.user.organization_id
      } 
    });
    
    await Drop.update({ status: 'deleted' }, { 
      where: { 
        shiftId: id,
        organization_id: req.user.organization_id
      } 
    });

    const oldShift: any = {
      ...findShift.toJSON()
    };
    const newShift: any = {
      ...findShift.toJSON(),
      ...updateObj
    };

    if (moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss") !== moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss")) {
      oldShift.startTime = moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss");
      oldShift.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.startTime = moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.endTime = moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss");
    } else if (moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss") !== moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss")) {
      oldShift.startTime = moment(findShift.startTime).format("YYYY-MM-DD HH:mm:ss");
      oldShift.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.startTime = moment(updateObj.startTime).format("YYYY-MM-DD HH:mm:ss");
      newShift.endTime = moment(updateObj.endTime).format("YYYY-MM-DD HH:mm:ss");
    }

    const changes = ShiftHistory.generateChanges(oldShift, newShift);
    await ShiftHistory.logChange(Number(id), req.user?.id, "UPDATE", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const notificationObj = {
      deviceId: findUser?.webAppToken
        ? findUser?.webAppToken
        : findUser?.appToken,
      content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
        findUser?.user_first_name,
        moment(startTime).format("YYYY-MM-DD HH:mm A"),
        moment(endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
          findUser?.user_first_name,
          moment(startTime).format("YYYY-MM-DD HH:mm A"),
          moment(endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
        notification_image: null,
        to_user_id: userId,
        redirection_type: "shift_details",
        notification_status: "sent",
        notification_type: "shifts_add",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
    };

    if (isPublished && acknowledged && !findShift.isPublished && !isOpen) {
      await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    }

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
const setAsOpenShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);
    if (!checkPermission)
      return res
        .status(403)
        .json({ status: false, message: res.__("PERMISSION_DENIED") });

    const findShift = await Shift.findByPk(id);
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    req.body = JSON.parse(JSON.stringify(findShift));
    req.body.startTime = moment(findShift.startTime).format(
      "YYYY-MM-DD HH:mm:ss",
    );
    req.body.endTime = moment(findShift.endTime).format("YYYY-MM-DD HH:mm:ss");

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      if (validateShift?.data?.id !== id) {
        return res.status(409).send({
          status: false,
          message: validateShift?.message,
        });
      }
    }

    await Shift.update(
      {
        userId: null,
        isOpen: true,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          id,
        },
      },
    );

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      userId: null,
      isOpen: true,
      updatedBy: req?.user?.id,
    });
    await ShiftHistory.logChange(Number(id), req.user?.id, "OPEN", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_SET_TO_OPEN"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const dropShift = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const findShift = await Shift.findByPk(id);
    if (!findShift || (findShift && findShift.userId != req.user.id)) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const findPreviousData = await Drop.findOne({
      where: {
        shiftId: id,
        organization_id: req.user.organization_id
      },
    });
    if(findPreviousData && findPreviousData.status === "pending") {
      return res.status(409).send({
        status: false,
        message: res.__("DROP_ALREADY_REQUESTED"),
      });
    }
    if(findPreviousData && findPreviousData.status === "deleted") {
      return res.status(409).send({
        status: false,
        message: res.__("DROP_ALREADY_DENIED"),
      });
    }


    await Shift.update(
      {
        isDropped: true,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          id,
          organization_id: req.user.organization_id
        },
      },
    );

    await Drop.create({
      shiftId: id,
      userId: req.user.id,
      createdBy: req.user.id,
      reason: reason,
      organization_id: req.user.organization_id
    })

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      isDropped: true,
      updatedBy: req?.user?.id,
    });
    await ShiftHistory.logChange(Number(id), req.user?.id, "DROP", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const findUser = await getUser(findShift.createdBy);

    const notificationObj = {
      deviceId: findUser?.webAppToken
        ? findUser?.webAppToken
        : findUser?.appToken,
      content: NOTIFICATIONS.SHIFT_DROP_REQUEST.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_DROP_REQUEST.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_DROP_REQUEST.HEADER,
        notification_image: null,
        to_user_id: findUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_drop",
        notification_status: "sent",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_DROP_REQUEST.HEADER,
    };

    await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_DROPPED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const dropAction = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    await Drop.update(
      {
        status,
        updatedBy: req?.user?.id,
      },
      {
        where: {
          shiftId: id,
          organization_id: req.user.organization_id
        },
      });

      const findUser = await getUser(findShift.userId);

    const notificationObj = {
      deviceId: findUser?.webAppToken
        ? findUser?.webAppToken
        : findUser?.appToken,
      content: NOTIFICATIONS.SHIFT_DROP_REJECT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_DROP_REJECT.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        notification_subject: NOTIFICATIONS.SHIFT_DROP_REJECT.HEADER,
        notification_image: null,
        to_user_id: findUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_drop",
        notification_status: "sent",
        reference_id: id,
        redirection_object: { id: id },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_DROP_REJECT.HEADER,
    };


    if(status == "active"){
      notificationObj.content = NOTIFICATIONS.SHIFT_DROP_ACCEPT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      )
      notificationObj.data.notification_content = NOTIFICATIONS.SHIFT_DROP_ACCEPT.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      )
      notificationObj.subject = NOTIFICATIONS.SHIFT_DROP_ACCEPT.HEADER
      notificationObj.data.notification_subject = NOTIFICATIONS.SHIFT_DROP_ACCEPT.HEADER

      const updateData = {
        isDropped: false,
        isPublished: false,
        isOpen: true,
        isSwap: false,
        userId: null,
        updatedBy: req?.user?.id,
      }
      const changes = ShiftHistory.generateChanges(findShift, {
        ...findShift,
        ...updateData
      });
      await ShiftHistory.logChange(Number(id), req.user?.id, "DROP", changes, {
        ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
        userAgent: `${req?.headers?.["user-agent"]}`,
      });
      await Shift.update(
        updateData,
        {
          where: {
            id,
            organization_id: req.user.organization_id
          },
        },
      );
    }else{
      await Shift.update(
        {
          isDropped: true,
          updatedBy: req?.user?.id,
        },
        {
          where: {
            id,
            organization_id: req.user.organization_id
          },
        },
      );
    }

    await createNotification(notificationObj?.data, req, notificationObj?.deviceId);

    return res.status(200).send({
      status: true,
      message: status == "active" ? res.__("DROP_SHIFT_APPROVED") : res.__("DROP_SHIFT_REJECTED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}

const checkShiftAvailability = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
        data: validateShift?.data,
      });
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFT_AVAILABLE"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const publishUnpublishShift = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { ids } = req.body;
    const findShift = await Shift.findAll({
      where: { id: { [Op.in]: ids } },
    });
    if (!findShift.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }
    const { isPublished } = req.body;
    await Shift.update(
      {
        isPublished: isPublished == true ? true : false,
        updatedBy: req?.user?.id,
      },
      { where: { id: { [Op.in]: findShift?.map((item: any) => item.id) } } },
    );

    for (const s of findShift) {

      const findUser = await getUser(s?.userId);

      const notificationObj = {
        deviceId: findUser?.webAppToken
          ? findUser?.webAppToken
          : findUser?.appToken,
        content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
          findUser?.user_first_name,
          moment(s.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(s.endTime).format("YYYY-MM-DD HH:mm A"),
        ),
        data: {
          notification_content: NOTIFICATIONS.SHIFT_UPDATE.CONTENT(
            findUser?.user_first_name,
            moment(s.startTime).format("YYYY-MM-DD HH:mm A"),
            moment(s.endTime).format("YYYY-MM-DD HH:mm A"),
          ),
          notification_subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
          notification_image: null,
          to_user_id: s.userId,
          redirection_type: "shift_details",
          notification_status: "sent",
          notification_type: "shifts_add",
          reference_id: s.id,
          redirection_object: { id: s.id },
          from_user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id
        },
        type: "shift",
        subject: NOTIFICATIONS.SHIFT_UPDATE.HEADER,
      };

      if (findUser) {
        if (isPublished && s.acknowledged && !s.isPublished && !s.isOpen) {
          await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
        }
      }

      const changes = ShiftHistory.generateChanges(s, {
        ...s,
        isPublished: isPublished == true ? true : false,
        updatedBy: req?.user?.id,
      });
      await ShiftHistory.logChange(
        s.id,
        req.user?.id,
        isPublished == true ? "PUBLISH" : "UNPUBLISH",
        changes,
        {
          ip: req?.headers?.["ip-address"]
            ? `${req?.headers?.["ip-address"]}`
            : "",
          userAgent: `${req?.headers?.["user-agent"]}`,
        },
      );
    }

    return res.status(200).send({
      status: true,
      message:
        isPublished == true
          ? res.__("SHIFT_PUBLISHED")
          : res.__("SHIFT_UNPUBLISHED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const swapShift = async (req: Request, res: Response): Promise<any> => {
  try {
    req.body.fromUserId = req.body.userId;
    req.body.userId = req.body.toUserId;
    const { shiftId, toUserId, roleId } = req.body;

    const findShift = await Shift.findOne({ 
      where: { 
        id: shiftId,
        organization_id: req.user.organization_id 
      }, 
      raw: true 
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }
    req.body.startTime = new Date(findShift.startTime);
    req.body.endTime = new Date(findShift.endTime);

    const validateShift = await commonShiftValidator(req);
    if (validateShift) {
      return res.status(409).send({
        status: false,
        message: validateShift?.message,
        data: validateShift?.data,
      });
    }

    if (roleId) {
      const findRole = await getRoles([roleId]);
      if (!findRole.length) {
        return res.status(404).send({
          status: false,
          message: res.__("ROLE_NOT_FOUND"),
        });
      }
    }

    const existSwap = await Swap.findOne({
      where: { 
        shiftId, 
        toUserId, 
        status: { [Op.not]: ShiftStatus.deleted },
        organization_id: req.user.organization_id
      },
    });

    if (existSwap) {
      return res.status(409).send({
        status: false,
        message: res.__("SWAP_EXISTS"),
      });
    }

    await Shift.update(
      { isSwap: 1 }, 
      { 
        where: { 
          id: shiftId,
          organization_id: req.user.organization_id 
        } 
      }
    );

    const changes = ShiftHistory.generateChanges(findShift, {
      ...findShift,
      isSwap: 1,
    });
    await ShiftHistory.logChange(findShift.id, req.user?.id, "SWAP", changes, {
      ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
      userAgent: `${req?.headers?.["user-agent"]}`,
    });

    const newSwap = await Swap.create({
      userId: req.body.fromUserId,
      toUserId,
      shiftId,
      roleId,
      createdBy: req?.user?.id,
      updatedBy: req?.user?.id,
      organization_id: req.user.organization_id
    });

    const findToUser = await getUser(toUserId);

    const notificationObj = {
      deviceId: findToUser?.webAppToken
        ? findToUser?.webAppToken
        : findToUser?.appToken,
      content: NOTIFICATIONS.SHIFT_SWAP.CONTENT(
        req.user?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        findToUser?.user_first_name,
      ),
      data: {
        notification_content: NOTIFICATIONS.SHIFT_SWAP.CONTENT(
          req.user?.user_first_name,
          moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
          moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
          findToUser?.user_first_name,
        ),
        notification_subject: NOTIFICATIONS.SHIFT_SWAP.HEADER,
        notification_image: null,
        to_user_id: findToUser.id,
        redirection_type: "shift_details",
        notification_type: "shifts_cover",
        notification_status: "sent",
        reference_id: shiftId,
        redirection_object: { id: shiftId },
        from_user_id: req.user.id,
        created_by: req.user.id,
        updated_by: req.user.id
      },
      type: "shift",
      subject: NOTIFICATIONS.SHIFT_SWAP.HEADER,
    };

    await createNotification(notificationObj?.data, req, notificationObj?.deviceId);

    return res.status(201).send({
      status: true,
      message: res.__("SWAP_CREATED"),
      data: newSwap,
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const swapActions = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id, status } = req.body;
    const findSwap = await Swap.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      }
    });
    if (!findSwap) {
      return res.status(404).send({
        status: false,
        message: res.__("SWAP_NOT_FOUND"),
      });
    }

    const updateObj: any = {
      updatedBy: req?.user?.id,
    }

    const isAdmin = await permittedForAdmin(req.user?.id, [ROLE_CONSTANT.SUPER_ADMIN, ROLE_CONSTANT.ADMIN, ROLE_CONSTANT.DIRECTOR, ROLE_CONSTANT.HR, ROLE_CONSTANT.BRANCH_MANAGER])


    const findUser = await getUser(findSwap.userId);
    const findToUser = await getUser(findSwap.toUserId);

    const findShift = await Shift.findOne({
      where: {
        id: findSwap.shiftId,
        organization_id: req.user.organization_id
      }
    });
    const adminUsers = await getUser(findShift.createdBy);

    if (findSwap.userId != req?.user?.id) {
      if (isAdmin && findToUser?.id != req?.user?.id) {
        if (findSwap.status == ShiftStatus.pending) {
          return res.status(400).send({
            status: false,
            message: `${findToUser?.user_first_name} must first reply to this swap request`,
          });
        }
        updateObj.adminStatus = status;
      }else{
        updateObj.status = status
      }
    } else {
      updateObj.status = status
    }

    await Swap.update(
      updateObj,
      { 
        where: { 
          id,
          organization_id: req.user.organization_id 
        } 
      },
    );

    let content = NOTIFICATIONS.SHIFT_SWAP_ACCEPT.CONTENT(
      findToUser?.user_first_name,
      moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
      moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
      findUser?.user_first_name,
    );
    let header = NOTIFICATIONS.SHIFT_SWAP_ACCEPT.HEADER;
    let deviceId = findToUser?.webAppToken
      ? findToUser?.webAppToken
      : findToUser?.appToken;
    let notificationUser = findToUser.id;
    if (status == ShiftStatus.active) {
      if ((isAdmin && findSwap.status == ShiftStatus.active) || ((!isAdmin && findSwap.adminStatus == ShiftStatus.active))) {
        await Shift.update(
          { userId: findSwap.toUserId, roleId: findSwap.roleId },
          { where: { id: findSwap.shiftId } },
        );
      }

      if (isAdmin) {
        // Send notification to both fromUser and toUser when admin accepts
        const notificationUsers = [findUser, findToUser];
        for (const user of notificationUsers) {
          const userDeviceId = user?.webAppToken ? user?.webAppToken : user?.appToken;
          const notificationObj = {
            deviceId: userDeviceId,
            content: content,
            data: {
              notification_content: content,
              notification_subject: header,
              notification_image: null,
              to_user_id: user.id,
              redirection_type: "shift_details",
              notification_type: "shifts_cover",
              notification_status: "sent",
              reference_id: findSwap.shiftId,
              redirection_object: { id: findSwap.shiftId },
              from_user_id: req.user.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
              updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
            },
            type: "shift",
            subject: header,
          };
          await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
        }
      } else {
        // Send notification to admin users and requester when toUser accepts
        const notificationUsers = [...adminUsers, findUser];
        for (const user of notificationUsers) {
          const userDeviceId = user?.webAppToken ? user?.webAppToken : user?.appToken;
          const notificationObj = {
            deviceId: userDeviceId,
            content: content,
            data: {
              notification_content: content,
              notification_subject: header,
              notification_image: null,
              to_user_id: user.id,
              redirection_type: "shift_details",
              notification_type: "shifts_cover",
              notification_status: "sent",
              reference_id: findSwap.shiftId,
              redirection_object: { id: findSwap.shiftId },
              from_user_id: req.user.id,
              created_by: req.user.id,
              updated_by: req.user.id,
              createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
              updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
            },
            type: "shift",
            subject: header,
          };
          await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
        }
      }
    } else {
      if (isAdmin || findSwap.userId == req.user?.id) {
        await Shift.update(
          { userId: findSwap.userId, isSwap: false },
          { where: { id: findSwap.shiftId } },
        );
      }
      content = NOTIFICATIONS.SHIFT_SWAP_REJECT.CONTENT(
        findUser?.user_first_name,
        moment(findShift.startTime).format("YYYY-MM-DD HH:mm A"),
        moment(findShift.endTime).format("YYYY-MM-DD HH:mm A"),
        findToUser?.user_first_name,
      );
      header = NOTIFICATIONS.SHIFT_SWAP_REJECT.HEADER;
      deviceId = findUser?.webAppToken
        ? findUser?.webAppToken
        : findUser?.appToken;
      notificationUser = findUser.id;

      const notificationObj = {
        deviceId: deviceId,
        content: content,
        data: {
          notification_content: content,
          notification_subject: header,
          notification_image: null,
          to_user_id: notificationUser,
          redirection_type: "shift_details",
          notification_type: "shifts_cover",
          notification_status: "sent",
          reference_id: findSwap.shiftId,
          redirection_object: { id: findSwap.shiftId },
          from_user_id: req.user.id,
          created_by: req.user.id,
          updated_by: req.user.id,
          createdAt: moment().format("YYYY-MM-DD HH:mm:ss"),
          updatedAt: moment().format("YYYY-MM-DD HH:mm:ss")
        },
        type: "shift",
        subject: header,
      };

      if (req.user.id != findShift.userId) {
        await createNotification(notificationObj?.data, req, notificationObj?.deviceId);
      }
    }

    return res.status(200).send({
      status: true,
      message:
        status == ShiftStatus.active
          ? res.__("SWAP_ACCEPTED")
          : res.__("SWAP_REJECTED"),
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};
const getShiftHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    const findShift = await Shift.findOne({
      where: {
        id,
        organization_id: req.user.organization_id
      },
      raw: true,
      nest: true
    });
    if (!findShift) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const findShiftHistory = await ShiftHistory.findAll({
      where: {
        shiftId: id,
      },
      order: [["createdAt", "DESC"]],
      raw: true,
      nest: true
    });

    await Promise.all(
      findShiftHistory.map(async (history: any) => {
        history.changes = history.changes ? JSON.parse(JSON.stringify(history.changes)) : {};
        history.metadata = history.metadata ? JSON.parse(JSON.stringify(history.metadata)) : {};
        history.user = await getUser(history.userId);
        return history;
      }),
    );

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_FETCHED"),
      data: {
        ...findShift,
        history: findShiftHistory,
      },
    });
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const copyShiftsRange = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      fromStartDate,
      fromEndDate,
      toStartDate,
      toEndDate,
      selectedEmployees, // Array of employee IDs to copy shifts for
      copyDaysOff,      // Boolean to indicate if days off should be copied
      shiftClashStrategy // How to handle shift clashes ("clear" | "skip" | "overwrite" | "openshift")
    } = req.body;

    if (!fromStartDate || !fromEndDate || !toStartDate || !toEndDate) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    if (!shiftClashStrategy || !["clear", "skip", "overwrite", "openshift"].includes(shiftClashStrategy)) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_SHIFT_CLASH_STRATEGY"),
      });
    }

    const fromStart = moment(fromStartDate).startOf("day");
    const fromEnd = moment(fromEndDate).endOf("day");
    const toStart = moment(toStartDate).startOf("day");
    const toEnd = moment(toEndDate).endOf("day");

    if (fromStart > fromEnd || toStart > toEnd) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    // Build query conditions
    const queryConditions: any = {
      startTime: {
        [Op.between]: [fromStart.toDate(), fromEnd.toDate()],
      },
      status: ShiftStatus.active,
      organization_id: req.user.organization_id
    };

    // If specific employees are selected, add to query
    if (selectedEmployees?.length) {
      queryConditions.userId = {
        [Op.in]: selectedEmployees
      };
    }

    // Clear existing shifts in target date range if strategy is "clear"
    if (shiftClashStrategy === "clear") {
      await Shift.update(
        { status: ShiftStatus.deleted },
        {
          where: {
            startTime: {
              [Op.between]: [toStart.toDate(), toEnd.toDate()],
            },
            organization_id: req.user.organization_id,
            ...(selectedEmployees?.length && { userId: { [Op.in]: selectedEmployees } })
          }
        }
      );
    }

    const shifts = await Shift.findAll({
      where: queryConditions
    });

    const fromDays = fromEnd.diff(fromStart, "days") + 1;
    const toDays = toEnd.diff(toStart, "days") + 1;

    if (fromDays > toDays) {
      return res.status(400).send({
        status: false,
        message: res.__("TO_DATE_RANGE_MUST_BE_GREATER_THAN_FROM_DATE_RANGE"),
      });
    }

    // Track results for response
    const results = {
      copiedShifts: 0,
      skippedShifts: 0,
      overwrittenShifts: 0,
      openShifts: 0,
      conflictDetails: [] as any[]
    };

    for (let i = 0; i < toDays; i++) {
      const currentFromStart = fromStart.clone().add(i % fromDays, "days");
      const currentToStart = toStart.clone().add(i, "days");

      for (const shift of shifts) {
        const duration = moment(shift.endTime).diff(
          moment(shift.startTime),
          "milliseconds"
        );

        const timeDiff = moment(shift.startTime).diff(
          currentFromStart.clone().startOf("day"),
          "milliseconds"
        );

        const newStartTime = currentToStart
          .clone()
          .startOf("day")
          .add(timeDiff, "milliseconds")
          .toDate();
        const newEndTime = moment(newStartTime)
          .add(duration, "milliseconds")
          .toDate();

        // Check for conflicts unless strategy is "clear" (already handled)
        let shouldCreateShift = true;
        let shiftToCreate = { ...shift.toJSON() };

        if (shiftClashStrategy !== "clear") {
          // Enhanced conflict detection with proper organization filtering
          const conflictQuery: any = {
            [Op.or]: [
              {
                startTime: {
                  [Op.and]: {
                    [Op.gte]: newStartTime,
                    [Op.lt]: newEndTime,
                  },
                },
              },
              {
                endTime: {
                  [Op.and]: {
                    [Op.gt]: newStartTime,
                    [Op.lte]: newEndTime,
                  },
                },
              },
              {
                startTime: {
                  [Op.lte]: newStartTime,
                },
                endTime: {
                  [Op.gt]: newEndTime,
                },
              },
            ],
            status: ShiftStatus.active,
            isDropped: false,
            organization_id: req.user.organization_id
          };

          // For openshift strategy, check for time clash with same user
          if (shiftClashStrategy === "openshift" && shift.userId) {
            conflictQuery.userId = shift.userId;
          }

          const existingShift = await Shift.findOne({
            where: conflictQuery,
          });

          if (existingShift) {
            const conflictDetail = {
              date: moment(newStartTime).format('YYYY-MM-DD'),
              time: `${moment(newStartTime).format("HH:mm")} - ${moment(newEndTime).format("HH:mm")}`,
              originalUserId: shift.userId,
              conflictingShiftId: existingShift.id,
              strategy: shiftClashStrategy
            };

            if (shiftClashStrategy === "skip") {
              // Skip this shift and continue with the next
              results.skippedShifts++;
              results.conflictDetails.push({ ...conflictDetail, action: "skipped" });
              shouldCreateShift = false;
            } else if (shiftClashStrategy === "openshift") {
              // Check if there's a time clash for the same user
              if (existingShift.userId === shift.userId) {
                // Time clash detected - do not copy this shift to openshift
                results.skippedShifts++;
                results.conflictDetails.push({
                  ...conflictDetail,
                  action: "skipped_due_to_time_clash",
                  reason: "Cannot convert to open shift due to time clash with existing shift for same user"
                });
                shouldCreateShift = false;
              } else {
                // No time clash with same user, convert to open shift
                shiftToCreate.userId = null;
                shiftToCreate.isOpen = true;
                results.openShifts++;
                results.conflictDetails.push({ ...conflictDetail, action: "converted_to_open" });
              }
            } else if (shiftClashStrategy === "overwrite") {
              // Delete the existing conflicting shift first
              await Shift.update(
                { status: ShiftStatus.deleted },
                {
                  where: {
                    id: existingShift.id,
                    organization_id: req.user.organization_id
                  }
                }
              );
              results.overwrittenShifts++;
              results.conflictDetails.push({ ...conflictDetail, action: "overwritten" });
            }
          }
        }

        // Create the shift only if it should be created
        if (shouldCreateShift) {
          const newShift = await Shift.create({
            ...shiftToCreate,
            id: undefined,
            startTime: newStartTime,
            endTime: newEndTime,
            organization_id: req.user.organization_id,
            createdBy: req.user.id,
            updatedBy: req.user.id,
            createdAt: undefined,
            updatedAt: undefined,
          });

          results.copiedShifts++;

          // Log shift history for the new shift
          const changes = ShiftHistory.generateChanges({}, newShift);
          await ShiftHistory.logChange(newShift.id, req.user?.id, "CREATE", changes, {
            ip: req?.headers?.["ip-address"] ? `${req?.headers?.["ip-address"]}` : "",
            userAgent: `${req?.headers?.["user-agent"]}`,
          });
        }
      }

      // Copy days off if enabled
      if (copyDaysOff) {
        // Add logic to copy DayOff records
        const dayOffs = await DayOff.findAll({
          where: {
            date: {
              [Op.between]: [fromStart.toDate(), fromEnd.toDate()],
            },
            organization_id: req.user.organization_id,
            ...(selectedEmployees?.length && { userId: { [Op.in]: selectedEmployees } })
          }
        });

        for (const dayOff of dayOffs) {
          const newDate = currentToStart
            .clone()
            .add(moment(dayOff.date).diff(fromStart))
            .toDate();

          await DayOff.create({
            ...dayOff.toJSON(),
            id: undefined,
            date: newDate,
            organization_id: req.user.organization_id,
            createdAt: undefined,
            updatedAt: undefined,
          });
        }
      }
    }

    // Prepare response message based on strategy and results
    let message = res.__("SHIFTS_COPIED_SUCCESSFULLY");
    if (results.skippedShifts > 0 || results.overwrittenShifts > 0 || results.openShifts > 0) {
      const details = [];
      if (results.copiedShifts > 0) details.push(`${results.copiedShifts} shifts copied`);
      if (results.skippedShifts > 0) details.push(`${results.skippedShifts} shifts skipped due to conflicts`);
      if (results.overwrittenShifts > 0) details.push(`${results.overwrittenShifts} shifts overwritten`);
      if (results.openShifts > 0) details.push(`${results.openShifts} shifts converted to open shifts`);
      message = `Shift copy completed: ${details.join(', ')}`;
    }

    return res.status(200).send({
      status: true,
      message: message,
      data: {
        summary: {
          copiedShifts: results.copiedShifts,
          skippedShifts: results.skippedShifts,
          overwrittenShifts: results.overwrittenShifts,
          openShifts: results.openShifts,
          strategy: shiftClashStrategy
        },
        conflictDetails: results.conflictDetails
      }
    });
  } catch (err) {
    console.log(err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};

const checkCopyShiftTimeConflict = async (
  req: Request,
  res: Response,
): Promise<any> => {
  try {
    const { fromStartDate, fromEndDate, toStartDate, toEndDate } = req.body;

    if (!fromStartDate || !fromEndDate || !toStartDate || !toEndDate) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const fromStart = moment(fromStartDate).startOf("day");
    const fromEnd = moment(fromEndDate).endOf("day");
    const toStart = moment(toStartDate).startOf("day");
    const toEnd = moment(toEndDate).endOf("day");

    if (fromStart > fromEnd || toStart > toEnd) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const daysDifferenceFrom = fromEnd.diff(fromStart, "days");
    const daysDifferenceTo = toEnd.diff(toStart, "days");

    if (daysDifferenceTo < daysDifferenceFrom) {
      return res.status(400).send({
        status: false,
        message: res.__("INVALID_DATE_RANGE"),
      });
    }

    const shifts = await Shift.findAll({
      where: {
        startTime: {
          [Op.between]: [fromStart.toDate(), fromEnd.toDate()],
        },
      },
    });

    if (!shifts.length) {
      return res.status(404).send({
        status: false,
        message: res.__("SHIFT_NOT_FOUND"),
      });
    }

    const shiftConflicts = [];
    let message = "";
    for (let i = 0; i <= daysDifferenceTo; i++) {
      const currentToStart = toStart.clone().add(i, "days");

      for (let j = 0; j < shifts.length; j++) {
        const shift = shifts[j];
        const duration = moment(shift.endTime).diff(
          moment(shift.startTime),
          "milliseconds",
        );
        const newStartTime = currentToStart
          .clone()
          .add(moment(shift.startTime).diff(fromStart))
          .toDate();
        const newEndTime = moment(newStartTime)
          .add(duration, "milliseconds")
          .toDate();

        const existingShift = await commonShiftValidator({
          ...req,
          body: {
            ...req.body,
            startTime: newStartTime,
            endTime: newEndTime,
            isOpen: shift.isOpen,
            userId: shift.userId,
          },
        });

        if (existingShift) {
          message = existingShift.message;
          if (existingShift?.data) shiftConflicts.push(existingShift?.data);
        }
      }
    }

    if (shiftConflicts.length) {
      return res.status(200).send({
        status: true,
        message: message ? message : res.__("SHIFTS_TIME_CONFLICT"),
        data: shiftConflicts,
      });
    }

    return res.status(200).send({
      status: true,
      message: res.__("SHIFTS_NO_CONFLICT"),
    });
  } catch (err) {
    console.error(err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
};

const claimOpenShift = async (req: Request, res: Response): Promise<any> => {
  try{
     const { id } = req.params
     const userId = req.user.id

     const findShift = await Shift.findOne({
       where: {
         id,
         organization_id: req.user.organization_id
       }, raw: true, nest: true})

     if (!findShift) {
       return res.status(404).send({
         status: false,
         message: res.__("SHIFT_NOT_FOUND"),
       });
     }

     const existingShift = await commonShiftValidator({
      ...req,
      body: {
        ...findShift,
        userId: userId,
      },
    });

    if (existingShift) {
      return res.status(200).send({
        status: false,
        message: existingShift.message,
      });
    }

     await Shift.update({
       isClaim: true,
       userId: userId,
       isOpen: false
     }, {
       where: {
         id,
         organization_id: req.user.organization_id
       }
     })

    return res.status(200).send({
       status: true,
       message: res.__("SHIFT_CLAIMED"),
     })

  }catch (err){
    console.error(err);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error: err,
    });
  }
}

const exportShiftsToExcel = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      userId,
      startDate,
      endDate,
      role,
      branchId,
      departmentId,
      isAdmin,
      isOpen,
      sortBy,
      sortOrder,
      status,
    }: any = req.query;

    if (!startDate || !endDate) {
      return res.status(400).send({
        status: false,
        message: res.__("START_DATE_AND_END_DATE_REQUIRED"),
      });
    }

    let where: any = {
      status: status ? status : { [Op.not]: ShiftStatus.deleted },
    };

    const checkPermission = await permittedForAdmin(req.user?.id, [
      ROLE_CONSTANT.SUPER_ADMIN,
      ROLE_CONSTANT.ADMIN,
      ROLE_CONSTANT.DIRECTOR,
      ROLE_CONSTANT.HR,
    ]);

    if (userId && role) {
      where = {
        ...where,
        [Op.or]: [
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
          },
          {
            userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId,
            roleId: { [Op.not]: role },
          },
          {
            roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role,
            isOpen: true,
          }
        ]
      }
    } else if (!userId && role) {
      where = {
        ...where,
        roleId: role?.toString()?.split(",")?.length > 1 ? { [Op.in]: role?.toString()?.split(",")?.map(Number) } : role
      }
    } else if (userId && !role) {
      if (isAdmin === "true" && checkPermission) {
        where = {
          ...where,
          userId: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId
        }
      } else {
        where = {
          ...where,
          userId: req?.user?.id
        }
      }
    }

    // Admin and user filtering logic
    if (isAdmin === "true" && checkPermission) {
      if (userId && !role) where.userId = userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId;
    }

    if (!checkPermission || isAdmin != "true") {
      where.isPublished = 1;
    }

    // Open shift condition
    if (isOpen === "true") {
      where.isOpen = true;
      where.userId = { [Op.is]: userId?.toString()?.split(",")?.length > 1 ? { [Op.in]: userId?.toString()?.split(",")?.map(Number) } : userId ? userId : null };
    }

    // Role and location filtering
    if (branchId) where.branchId = branchId?.toString()?.split(",")?.length > 1 ? { [Op.in]: branchId?.toString()?.split(",")?.map(Number) } : branchId;
    if (departmentId) where.departmentId = departmentId;

    // Date range filtering
    where.startTime = { [Op.gte]: moment(startDate).startOf("day").toDate() };
    where.endTime = { [Op.lte]: moment(endDate).endOf("day").toDate() };

    // Fetch shifts
    const { rows: shifts, count }: any = await Shift.findAndCountAll({
      where,
      order: [
        [
          sortBy ? sortBy : "startTime",
          sortOrder && sortOrder != ""
            ? String(sortOrder).toUpperCase()
            : "ASC",
        ],
      ],
      raw: true,
      nest: true,
    });

    if (!shifts.length) {
      return res.status(404).send({
        status: false,
        message: res.__("NO_SHIFTS_FOUND"),
      });
    }

    // Extract unique IDs for batch fetching
    const userIds = [
      ...new Set(shifts.map((s: any) => s.userId).filter(Boolean)),
    ];
    const roleIds = [
      ...new Set(shifts.map((s: any) => s.roleId).filter(Boolean)),
    ];
    const locationIds = [
      ...new Set(shifts.map((s: any) => s.branchId).filter(Boolean)),
    ];
    const departmentIds = [
      ...new Set(shifts.map((s: any) => s.departmentId).filter(Boolean)),
    ];

    // Batch fetch related data
    const [users, roles, locations, departments] = await Promise.all([
      userIds.length ? getUsers(userIds) : [],
      roleIds.length ? getRoles(roleIds) : [],
      locationIds.length ? getBranchDetails(locationIds) : [],
      departmentIds.length ? getDepartmentDetails(departmentIds) : [],
    ]);

    // Create lookup maps for faster access
    const userMap = Object.fromEntries(users.map((u: any) => [u.id, u]));
    const roleMap = Object.fromEntries(roles.map((r: any) => [r.id, r]));
    const locationMap = Object.fromEntries(
      locations.map((l: any) => [l.id, l]),
    );
    const departmentsMap = Object.fromEntries(
      departments.map((l: any) => [l.id, l]),
    );

    // Process shifts and add related data
    const processedShifts = shifts.map((shift: any) => {
      const user = userMap[shift.userId] || null;
      const role = roleMap[shift.roleId] || null;
      const branch = locationMap[shift.branchId] || null;
      const department = departmentsMap[shift.departmentId] || null;

      return {
        id: shift.id,
        date: moment(shift.startTime).format("YYYY-MM-DD"),
        startTime: moment(shift.startTime).format("HH:mm"),
        endTime: moment(shift.endTime).format("HH:mm"),
        duration: moment.duration(moment(shift.endTime).diff(moment(shift.startTime))).asHours().toFixed(2),
        breakTime: shift.minutesBreak ? shift.minutesBreak : 0,
        userName: !shift.isOpen && user ? `${user.user_first_name} ${user.user_last_name}` : 'Open Shift',
        userEmail: !shift.isOpen && user ? user.user_email : '',
        roleName: role ? role.role_name : '',
        branchName: branch ? branch.branch_name : '',
        departmentName: department ? department.department_name : '',
        status: shift.status,
        isOpen: shift.isOpen ? 'Yes' : 'No',
        isPublished: shift.isPublished ? 'Yes' : 'No',
        notes: shift.notes || '',
      };
    });

    // Create Excel workbook and worksheet
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Shifts');

    // Add headers
    worksheet.columns = [
      { header: 'Shift ID', key: 'id', width: 10 },
      { header: 'Date', key: 'date', width: 12 },
      { header: 'Start Time', key: 'startTime', width: 12 },
      { header: 'End Time', key: 'endTime', width: 12 },
      { header: 'Duration (hrs)', key: 'duration', width: 15 },
      { header: 'Break Time (mins)', key: 'breakTime', width: 15 },
      { header: 'Employee Name', key: 'userName', width: 20 },
      { header: 'Email', key: 'userEmail', width: 25 },
      { header: 'Role', key: 'roleName', width: 15 },
      { header: 'Branch', key: 'branchName', width: 15 },
      { header: 'Department', key: 'departmentName', width: 15 },
      { header: 'Status', key: 'status', width: 12 },
      { header: 'Open Shift', key: 'isOpen', width: 10 },
      { header: 'Published', key: 'isPublished', width: 10 },
      { header: 'Notes', key: 'notes', width: 30 },
    ];

    // Style the header row
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add rows
    processedShifts.forEach((shift: any) => {
      worksheet.addRow(shift);
    });

    // Auto filter columns
    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: 14 }
    };

    // Set content type and disposition
    try {
      const buffer = await workbook.xlsx.writeBuffer();

      // Set response headers for Excel download
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename=shifts_${moment(startDate).format('YYYY-MM-DD')}_to_${moment(endDate).format('YYYY-MM-DD')}.xlsx`);

      // Send the buffer as a response
      return res.status(200).send(buffer);
    } catch (bufferError) {
      console.log("Error generating Excel buffer:", bufferError);
      return res.status(500).send({
        status: false,
        message: res.__("ERROR_GENERATING_EXCEL"),
        error: bufferError,
      });
    }
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
};

const changeGroupBy = async (req: Request, res: Response): Promise<any> => {
  try {
    const { groupBy } = req.body;

    if(groupBy != ""){
      await sequelize.query(`UPDATE nv_users SET rota_group_by = '${groupBy}' WHERE id = ${req.user.id}`);
    }else{
      await sequelize.query(`UPDATE nv_users SET rota_group_by = NULL WHERE id = ${req.user.id}`);
    }

    return res.status(200).send({
      status: true,
      message: res.__("SUCCESS_DATA_UPDATED"),
    });
  }catch(error){
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error,
    });
  }
}

const updateUserOrderList = async (req: Request, res: Response): Promise<any> => {
  try {
    const { sortOrder, userId } = req.body;

    // Get current user's order
    const currentUser = await getUser(userId)

    if (!currentUser) {
      return res.status(404).send({
        status: false,
        message: res.__("USER_NOT_FOUND")
      });
    }

    const currentOrder = currentUser?.list_order || null;

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      if (currentOrder === null || currentOrder === sortOrder) {
        // If user doesn't have an order yet or same order, simply update
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      } else if (currentOrder < sortOrder) {
        // Moving down: Shift everyone up who is between current and new position
        await sequelize.query(
          `UPDATE nv_users
           SET list_order = list_order - 1
           WHERE list_order > ? AND list_order <= ? AND id != ?`,
          {
            replacements: [currentOrder, sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );

        // Set new position
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      } else {
        // Moving up: Shift everyone down who is between new and current position
        await sequelize.query(
          `UPDATE nv_users
           SET list_order = list_order + 1
           WHERE list_order >= ? AND list_order < ? AND id != ?`,
          {
            replacements: [sortOrder, currentOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );

        // Set new position
        await sequelize.query(
          `UPDATE nv_users SET list_order = ? WHERE id = ?`,
          {
            replacements: [sortOrder, userId],
            type: QueryTypes.UPDATE,
            transaction
          }
        );
      }

      // Commit transaction
      await transaction.commit();

      return res.status(200).send({
        status: true,
        message: res.__("SUCCESS_DATA_UPDATED")
      });
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.log(error);
    return res.status(500).send({
      status: false,
      message: res.__("SOMETHING_WENT_WRONG"),
      error
    });
  }
};


export {
  addShift,
  getShifts,
  updateShift,
  checkShiftAvailability,
  setAsOpenShift,
  publishUnpublishShift,
  swapShift,
  swapActions,
  dropShift,
  getShiftHistory,
  copyShiftsRange,
  checkCopyShiftTimeConflict,
  deleteShift,
  clearWeekShifts,
  dropAction,
  claimOpenShift,
  exportShiftsToExcel,
  changeGroupBy,
  updateUserOrderList
};


